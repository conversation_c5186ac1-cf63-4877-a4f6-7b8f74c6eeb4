import React from 'react';
import { getFormattedVersion, getBuildEnvironment } from '../utils/version';
import './Footer.css';

const Footer = () => {
  const currentYear = 2025;
  const version = getFormattedVersion();
  const environment = getBuildEnvironment();

  return (
    <footer className="naroop-footer">
      <div className="footer-content">
        <div className="footer-brand">
          <h4>NAROOP</h4>
          <p>Narrative of Our People</p>
        </div>

        <div className="footer-links">
          <a href="#stories">Stories</a>
          <a href="/kids">Kids Zone</a>
          <a href="#about">About</a>
          <a href="#contact">Contact</a>
        </div>
      </div>

      <div className="footer-bottom">
        <p>&copy; {currentYear} NAROOP. Celebrating Black excellence.</p>
        <span className="version-badge">{version}</span>
      </div>
    </footer>
  );
};

export default Footer;
