import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useAuth } from '../AuthContext';
import ContentActions from './ContentActions';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';

const FINANCIAL_GOALS = [
  {
    id: 'homeownership',
    title: 'Homeownership',
    description: 'Build wealth through property ownership',
    icon: '🏠',
    resources: [
      { name: 'First-Time Homebuyer Programs', url: 'https://www.hud.gov/topics/buying_a_home' },
      { name: 'Black Homeownership Initiative', url: 'https://www.nareb.com/' },
      { name: 'Down Payment Assistance Programs', url: 'https://www.downpaymentresource.com/' }
    ]
  },
  {
    id: 'education',
    title: 'Debt-Free Education',
    description: 'Pursue education without financial burden',
    icon: '🎓',
    resources: [
      { name: 'UNCF Scholarships', url: 'https://www.uncf.org/scholarships' },
      { name: 'Black Excellence Scholarships', url: 'https://www.blackexcellence.com/scholarships' },
      { name: 'Student Loan Forgiveness Programs', url: 'https://studentaid.gov/manage-loans/forgiveness-cancellation' }
    ]
  },
  {
    id: 'business',
    title: 'Business Ownership',
    description: 'Create generational wealth through entrepreneurship',
    icon: '💼',
    resources: [
      { name: 'SBA Minority Business Programs', url: 'https://www.sba.gov/business-guide/grow-your-business/minority-owned-businesses' },
      { name: 'Black Business Investment Fund', url: 'https://www.mbda.gov/' },
      { name: 'SCORE Mentorship', url: 'https://www.score.org/' }
    ]
  },
  {
    id: 'emergency_fund',
    title: 'Emergency Fund',
    description: 'Build financial security and stability',
    icon: '💰',
    resources: [
      { name: 'Financial Planning Resources', url: 'https://www.nfec.org/' },
      { name: 'Black-Owned Banks Directory', url: 'https://www.oneunited.com/black-owned-banks/' },
      { name: 'Investment Education', url: 'https://www.investopedia.com/financial-literacy-4427896' }
    ]
  }
];

const MILESTONE_TEMPLATES = {
  homeownership: [
    { title: 'Check credit score', target: 650, description: 'Aim for 650+ credit score' },
    { title: 'Save for down payment', target: 20000, description: 'Save $20,000 for down payment' },
    { title: 'Get pre-approved', target: 1, description: 'Get mortgage pre-approval' },
    { title: 'Find a realtor', target: 1, description: 'Connect with a trusted realtor' }
  ],
  education: [
    { title: 'Research scholarships', target: 10, description: 'Apply to 10+ scholarships' },
    { title: 'Complete FAFSA', target: 1, description: 'Submit financial aid application' },
    { title: 'Explore work-study', target: 1, description: 'Look into work-study programs' },
    { title: 'Consider community college', target: 1, description: 'Evaluate 2-year transfer options' }
  ],
  business: [
    { title: 'Write business plan', target: 1, description: 'Complete comprehensive business plan' },
    { title: 'Register business', target: 1, description: 'Legal business registration' },
    { title: 'Secure funding', target: 10000, description: 'Raise initial capital' },
    { title: 'Launch MVP', target: 1, description: 'Launch minimum viable product' }
  ],
  emergency_fund: [
    { title: 'Save first $1,000', target: 1000, description: 'Build starter emergency fund' },
    { title: 'Track expenses', target: 30, description: 'Track spending for 30 days' },
    { title: 'Automate savings', target: 1, description: 'Set up automatic transfers' },
    { title: 'Reach 3-month expenses', target: 5000, description: 'Save 3 months of expenses' }
  ]
};

export default function EconomicEmpowerment() {
  const { currentUser } = useAuth();
  const [userGoals, setUserGoals] = useState([]);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadUserGoals();
  }, [currentUser]);

  const loadUserGoals = async () => {
    if (!currentUser) return;
    try {
      const userRef = doc(db, 'users', currentUser.uid);
      const userSnap = await getDoc(userRef);
      if (userSnap.exists()) {
        const userData = userSnap.data();
        setUserGoals(userData.financialGoals || []);
      }
    } catch (error) {
      console.error('Error loading goals:', error);
    }
    setLoading(false);
  };

  const addGoal = async (goalType, customTarget = null) => {
    if (!currentUser) return;
    
    const goalTemplate = FINANCIAL_GOALS.find(g => g.id === goalType);
    const milestones = MILESTONE_TEMPLATES[goalType] || [];
    
    const newGoal = {
      id: Date.now().toString(),
      type: goalType,
      title: goalTemplate.title,
      description: goalTemplate.description,
      icon: goalTemplate.icon,
      targetAmount: customTarget,
      currentAmount: 0,
      milestones: milestones.map(m => ({ ...m, completed: false })),
      createdAt: new Date().toISOString(),
      targetDate: null
    };

    const updatedGoals = [...userGoals, newGoal];
    setUserGoals(updatedGoals);

    try {
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, { financialGoals: updatedGoals });
    } catch (error) {
      console.error('Error saving goal:', error);
      setError('Failed to add financial goal. Please check your connection and try again.');
      // Revert the optimistic update
      setUserGoals(userGoals);
    }
  };

  const handleGoalDeleted = (goalId) => {
    setUserGoals(prev => prev.filter(goal => goal.id !== goalId));
    // Close modal if the deleted goal was selected
    if (selectedGoal && selectedGoal.id === goalId) {
      setSelectedGoal(null);
    }
  };

  const handleMilestoneDeleted = (goalId, milestoneIndex) => {
    setUserGoals(prev => prev.map(goal => {
      if (goal.id === goalId) {
        return {
          ...goal,
          milestones: goal.milestones.filter((_, index) => index !== milestoneIndex)
        };
      }
      return goal;
    }));

    // Update selectedGoal if it's the current one
    if (selectedGoal && selectedGoal.id === goalId) {
      const updatedGoal = userGoals.find(g => g.id === goalId);
      if (updatedGoal) {
        setSelectedGoal({
          ...updatedGoal,
          milestones: updatedGoal.milestones.filter((_, index) => index !== milestoneIndex)
        });
      }
    }
  };

  const updateGoalProgress = useCallback(async (goalId, field, value) => {
    const updatedGoals = userGoals.map(goal =>
      goal.id === goalId ? { ...goal, [field]: value } : goal
    );
    setUserGoals(updatedGoals);

    try {
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, { financialGoals: updatedGoals });
    } catch (error) {
      console.error('Error updating goal:', error);
    }
  }, [userGoals, currentUser.uid]);

  const toggleMilestone = useCallback(async (goalId, milestoneIndex) => {
    const updatedGoals = userGoals.map(goal => {
      if (goal.id === goalId) {
        const updatedMilestones = goal.milestones.map((milestone, index) =>
          index === milestoneIndex
            ? { ...milestone, completed: !milestone.completed }
            : milestone
        );
        return { ...goal, milestones: updatedMilestones };
      }
      return goal;
    });

    setUserGoals(updatedGoals);

    try {
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, { financialGoals: updatedGoals });
    } catch (error) {
      console.error('Error updating milestone:', error);
    }
  }, [userGoals, currentUser.uid]);

  // Memoize goals with calculated progress to avoid recalculation on every render
  const goalsWithProgress = useMemo(() => {
    return userGoals.map(goal => {
      const completedMilestones = goal.milestones.filter(m => m.completed).length;
      const progressPercent = goal.milestones.length > 0
        ? (completedMilestones / goal.milestones.length) * 100
        : 0;

      return {
        ...goal,
        completedMilestones,
        progressPercent
      };
    });
  }, [userGoals]);

  if (loading) return <div className="economic-empowerment-loading">Loading your financial goals...</div>;

  return (
    <div className="naroop-page-container">
      <div className="naroop-page-header">
        <h2>💪 Economic Empowerment Hub</h2>
        <p>Build generational wealth and achieve financial freedom</p>
      </div>

      {error && (
        <div className="naroop-error-message">
          {error}
        </div>
      )}

      {userGoals.length === 0 ? (
        <div className="naroop-section">
          <div className="naroop-section-header">
            <h3>🎯 Start Your Wealth-Building Journey</h3>
            <p>Choose a financial goal to begin tracking your progress:</p>
          </div>
          <div className="naroop-cards-grid">
            {FINANCIAL_GOALS.map(goal => (
              <div
                key={goal.id}
                className="naroop-card naroop-card-interactive"
                onClick={() => addGoal(goal.id)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    addGoal(goal.id);
                  }
                }}
                aria-label={`Add ${goal.title} financial goal`}
              >
                <div className="naroop-card-icon">{goal.icon}</div>
                <h4>{goal.title}</h4>
                <p>{goal.description}</p>
                <div className="naroop-card-meta">
                  <small>{goal.resources.length} resources available</small>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="naroop-section">
          <div className="naroop-section-header">
            <h3>Your Financial Goals ({userGoals.length})</h3>
            <button
              className="naroop-btn naroop-btn-primary"
              onClick={() => setShowGoalForm(true)}
              disabled={!currentUser}
              title={!currentUser ? "Please log in to add financial goals" : "Add a new financial goal"}
            >
              + Add New Goal
            </button>
          </div>

          <div className="naroop-cards-grid">
            {goalsWithProgress.map(goal => {
              return (
                <div key={goal.id} className="naroop-card">
                  <div className="naroop-card-header">
                    <span className="naroop-card-icon">{goal.icon}</span>
                    <h4>{goal.title}</h4>
                  </div>
                  
                  <div className="naroop-progress-section">
                    <div className="naroop-progress-bar">
                      <div
                        className="naroop-progress-fill"
                        style={{ width: `${goal.progressPercent}%` }}
                      ></div>
                    </div>
                    <span className="naroop-progress-text">
                      {goal.completedMilestones}/{goal.milestones.length} milestones
                    </span>
                  </div>

                  {goal.targetAmount && (
                    <div className="naroop-form-group">
                      <label className="naroop-form-label">
                        Progress: ${goal.currentAmount.toLocaleString()} / ${goal.targetAmount.toLocaleString()}
                      </label>
                      <input
                        type="number"
                        value={goal.currentAmount}
                        onChange={(e) => updateGoalProgress(goal.id, 'currentAmount', parseInt(e.target.value) || 0)}
                        className="naroop-form-input"
                        placeholder="Enter current amount"
                        min="0"
                        max={goal.targetAmount}
                      />
                    </div>
                  )}

                  <div className="naroop-card-content">
                    {goal.milestones.slice(0, 3).map((milestone, index) => (
                      <div key={index} className="naroop-milestone-item">
                        <input
                          type="checkbox"
                          checked={milestone.completed}
                          onChange={() => toggleMilestone(goal.id, index)}
                        />
                        <span className={milestone.completed ? 'completed' : ''}>
                          {milestone.title}
                        </span>
                      </div>
                    ))}
                    {goal.milestones.length > 3 && (
                      <button
                        className="naroop-btn naroop-btn-secondary"
                        onClick={() => setSelectedGoal(goal)}
                      >
                        View all {goal.milestones.length} milestones
                      </button>
                    )}
                  </div>

                  <div className="naroop-card-resources">
                    <h5>📚 Resources</h5>
                    {FINANCIAL_GOALS.find(g => g.id === goal.type)?.resources.slice(0, 2).map((resource, index) => (
                      <a key={index} href={resource.url} target="_blank" rel="noopener noreferrer" className="naroop-link">
                        {resource.name}
                      </a>
                    ))}
                  </div>

                  <div className="naroop-card-actions">
                    <ContentActions
                      contentType="financialGoal"
                      contentId={goal.id}
                      contentData={goal}
                      userId={currentUser?.uid}
                      onDeleted={() => handleGoalDeleted(goal.id)}
                      onError={(error) => setError(error)}
                      showEdit={false}
                      showArchive={true}
                      className="goal-actions"
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {showGoalForm && (
        <div className="naroop-modal-overlay">
          <div className="naroop-modal">
            <div className="naroop-modal-header">
              <h3>💰 Add New Financial Goal</h3>
              <button
                className="naroop-modal-close"
                onClick={() => setShowGoalForm(false)}
                aria-label="Close modal"
              >
                ✕
              </button>
            </div>
            <div className="naroop-modal-content">
              <p>Choose a financial goal template to get started with your wealth-building journey</p>
              <div className="naroop-cards-grid">
                {FINANCIAL_GOALS.map(goal => (
                  <div
                    key={goal.id}
                    className="naroop-card naroop-card-interactive"
                    onClick={() => {
                      addGoal(goal.id);
                      setShowGoalForm(false);
                    }}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        addGoal(goal.id);
                        setShowGoalForm(false);
                      }
                    }}
                  >
                    <div className="naroop-card-icon">{goal.icon}</div>
                    <h4>{goal.title}</h4>
                    <p>{goal.description}</p>
                    <div className="naroop-card-action">
                      Click to add this goal →
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="naroop-modal-actions">
              <button onClick={() => setShowGoalForm(false)} className="naroop-btn naroop-btn-secondary">
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {selectedGoal && (
        <div className="naroop-modal-overlay">
          <div className="naroop-modal naroop-modal-large">
            <div className="naroop-modal-header">
              <h3>{selectedGoal.icon} {selectedGoal.title} - All Milestones</h3>
              <button
                className="naroop-modal-close"
                onClick={() => setSelectedGoal(null)}
                aria-label="Close milestone view"
              >
                ✕
              </button>
            </div>

            <div className="milestone-modal-body">
              <div className="milestone-progress-summary">
                <div className="progress-stats">
                  <span className="completed-count">
                    {selectedGoal.milestones.filter(m => m.completed).length}
                  </span>
                  <span className="total-count">
                    / {selectedGoal.milestones.length} completed
                  </span>
                </div>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{
                      width: `${selectedGoal.milestones.length > 0
                        ? (selectedGoal.milestones.filter(m => m.completed).length / selectedGoal.milestones.length) * 100
                        : 0}%`
                    }}
                  ></div>
                </div>
                {selectedGoal.milestones.length > 10 && (
                  <p className="large-list-notice">
                    📋 Showing all {selectedGoal.milestones.length} milestones.
                    Use the scroll bar to browse through all items.
                  </p>
                )}
              </div>

              <div className="all-milestones-list">
                {selectedGoal.milestones.map((milestone, index) => (
                  <div key={index} className={`milestone-item-detailed ${milestone.completed ? 'completed' : ''}`}>
                    <div className="milestone-checkbox-container">
                      <input
                        type="checkbox"
                        id={`milestone-${selectedGoal.id}-${index}`}
                        checked={milestone.completed}
                        onChange={() => toggleMilestone(selectedGoal.id, index)}
                      />
                      <label htmlFor={`milestone-${selectedGoal.id}-${index}`} className="milestone-checkbox-label">
                        <span className="milestone-title">{milestone.title}</span>
                        {milestone.description && (
                          <span className="milestone-description">{milestone.description}</span>
                        )}
                        {milestone.target && (
                          <span className="milestone-target">Target: {milestone.target}</span>
                        )}
                      </label>
                    </div>
                    <div className="milestone-status">
                      {milestone.completed ? (
                        <span className="status-completed">✅ Completed</span>
                      ) : (
                        <span className="status-pending">⏳ Pending</span>
                      )}
                    </div>
                    <ContentActions
                      contentType="milestone"
                      contentId={`${selectedGoal.id}-${index}`}
                      contentData={{
                        goalId: selectedGoal.id,
                        milestoneIndex: index,
                        milestone: milestone
                      }}
                      userId={currentUser?.uid}
                      onDeleted={() => handleMilestoneDeleted(selectedGoal.id, index)}
                      onError={(error) => setError(error)}
                      showEdit={false}
                      showArchive={false}
                      className="milestone-actions"
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="milestone-modal-footer">
              <button
                className="close-modal-btn secondary"
                onClick={() => setSelectedGoal(null)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
