/* Simplified Footer Styles */
.naroop-footer {
  background: #f8f9fa;
  color: #591C28;
  margin-top: 40px;
  padding: 30px 20px 20px 20px;
  border-top: 2px solid #e9ecef;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.footer-brand h4 {
  color: #591C28;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 5px 0;
}

.footer-brand p {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

.footer-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #591C28;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: #6E8C65;
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.footer-bottom p {
  margin: 0;
  font-size: 0.85rem;
  color: #6c757d;
}

.version-badge {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #591C28;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .footer-links {
    justify-content: center;
    gap: 20px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
